"use client";

import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useOrganization } from '@/contexts/OrganizationContext';
import { queryKeys } from '@/api/hooks';

/**
 * Hook that listens for organization changes and refreshes relevant data
 * 
 * @param options Configuration options
 * @param options.refreshDocuments Whether to refresh documents when organization changes
 * @param options.refreshConversations Whether to refresh conversations when organization changes
 * @param options.refreshPersonas Whether to refresh personas when organization changes
 * @param options.refreshSettings Whether to refresh chat settings when organization changes
 * @returns void
 */
export function useOrganizationRefresh({
  refreshDocuments = true,
  refreshConversations = true,
  refreshPersonas = true,
  refreshSettings = true,
} = {}) {
  const { currentOrganization, onOrganizationChange } = useOrganization();
  const queryClient = useQueryClient();

  // Set up listener for organization changes
  useEffect(() => {
    // Function to handle organization changes
    const handleOrgChange = (org: any) => {
      if (!org) return;

      console.log('Organization changed, refreshing data...', org.id);

      // Invalidate relevant queries based on options
      if (refreshDocuments) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.documents(org.id)
        });
      }

      if (refreshConversations) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.conversations(org.id)
        });
      }

      if (refreshPersonas) {
        // Personas don't have a specific query key structure yet
        queryClient.invalidateQueries({ 
          queryKey: ['personas']
        });
      }

      if (refreshSettings) {
        // Chat settings don't have a specific query key structure yet
        queryClient.invalidateQueries({ 
          queryKey: ['chatSettings']
        });
      }
    };

    // Register the listener
    const unsubscribe = onOrganizationChange(handleOrgChange);

    // Clean up on unmount
    return () => {
      unsubscribe();
    };
  }, [
    onOrganizationChange, 
    queryClient, 
    refreshDocuments, 
    refreshConversations, 
    refreshPersonas, 
    refreshSettings
  ]);

  // Also trigger refresh when the hook is first mounted
  useEffect(() => {
    if (currentOrganization) {
      console.log('Initial organization data refresh...', currentOrganization.id);
      
      if (refreshDocuments) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.documents(currentOrganization.id)
        });
      }

      if (refreshConversations) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.conversations(currentOrganization.id)
        });
      }

      if (refreshPersonas) {
        queryClient.invalidateQueries({ 
          queryKey: ['personas']
        });
      }

      if (refreshSettings) {
        queryClient.invalidateQueries({ 
          queryKey: ['chatSettings']
        });
      }
    }
  }, [
    currentOrganization, 
    queryClient, 
    refreshDocuments, 
    refreshConversations, 
    refreshPersonas, 
    refreshSettings
  ]);
}
