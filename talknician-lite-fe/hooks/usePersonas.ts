"use client";

import { useState, useEffect } from "react";
import { ChatPersona } from "@/types/chat";
import { usePersonaApi } from "@/api/personas";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";

export function usePersonas() {
  const [personas, setPersonas] = useState<ChatPersona[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const apiClient = usePersonaApi();
  const { accessToken, isAuthenticated, user } = useAuth();
  const { currentOrganization } = useOrganization();

  const loadPersonas = async () => {
    try {
      setIsLoading(true);
      setError(null);
      if (!user?.organizations || user?.organizations.length === 0) {
        return;
      }

      const personaList = await apiClient.getAllPersonas(
        currentOrganization?.id
      );
      setPersonas(personaList);
    } catch (err) {
      console.error("Failed to load personas:", err);
      setError(err instanceof Error ? err.message : "Failed to load personas");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated && accessToken) {
      loadPersonas();
    } else {
      console.log("Skipping persona load - not authenticated:", {
        isAuthenticated,
        hasToken: !!accessToken,
      });
    }
  }, [isAuthenticated, accessToken]);

  const getPersona = (personaId: string | null): ChatPersona | null => {
    if (!personaId) return null;
    return personas.find((p) => p.id === personaId) || null;
  };

  const refreshPersonas = () => {
    if (isAuthenticated && accessToken) {
      loadPersonas();
    }
  };

  return {
    personas,
    isLoading,
    error,
    getPersona,
    refreshPersonas,
  };
}
