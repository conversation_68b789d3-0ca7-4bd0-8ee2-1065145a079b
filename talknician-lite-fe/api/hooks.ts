import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useDocumentApi, Document } from "./documents";
import { useOrganizationApi, Organization } from "./organizations";
import { useConversationApi } from "./conversations";
import { useChatApi } from "./chat";
import { useOneDriveApi, OneDriveAddToRAGRequest } from "./onedrive";
import { useBaseApiClient } from "./base";
import { MessageDocument } from "./message-document";

// Query Keys
export const queryKeys = {
  conversations: (orgId: string) => ["conversations", orgId] as const,
  conversation: (id: string) => ["conversation", id] as const,
  documents: (orgId: string) => ["documents", orgId] as const,
  organizations: () => ["organizations"] as const,
  onedrive: {
    status: () => ["onedrive", "status"] as const,
    items: (path: string) => ["onedrive", "items", path] as const,
  },
};

// Document Hooks
export const useDocumentsQuery = (organizationId: string) => {
  const documentApi = useDocumentApi();

  return useQuery({
    queryKey: queryKeys.documents(organizationId),
    queryFn: () => documentApi.getDocuments(organizationId),
    enabled: !!organizationId,
    staleTime: 30 * 1000, // 30 seconds - shorter for better responsiveness
    refetchInterval: (query) => {
      // Poll every 3 seconds if there are any processing documents
      const apiResponse = query.state.data;
      if (!apiResponse?.data) return false;

      const hasProcessingDocs = apiResponse.data.some(
        (doc: Document) => doc.status === "PROCESSING"
      );
      return hasProcessingDocs ? 3000 : false;
    },
    refetchIntervalInBackground: true,
  });
};

export const useUploadDocumentMutation = () => {
  const documentApi = useDocumentApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      file,
      organizationId,
    }: {
      file: File;
      organizationId: string;
    }) => documentApi.uploadDocument(file, organizationId),
    onSuccess: (response, variables) => {
      // Invalidate documents list
      queryClient.invalidateQueries({
        queryKey: queryKeys.documents(variables.organizationId),
      });
    },
  });
};

export const useDeleteDocumentMutation = () => {
  const documentApi = useDocumentApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => documentApi.deleteDocument(id),
    onSuccess: () => {
      // Invalidate documents lists
      queryClient.invalidateQueries({ queryKey: ["documents"] });
    },
  });
};

export const useAddDocumentToRAGMutation = () => {
  const documentApi = useDocumentApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => documentApi.addDocumentToRAG(id),
    onSuccess: () => {
      // Invalidate documents lists to refresh the inRAG status
      queryClient.invalidateQueries({ queryKey: ["documents"] });
    },
  });
};

export const useRemoveDocumentFromRAGMutation = () => {
  const documentApi = useDocumentApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => documentApi.removeDocumentFromRAG(id),
    onSuccess: () => {
      // Invalidate documents lists to refresh the inRAG status
      queryClient.invalidateQueries({ queryKey: ["documents"] });
    },
  });
};

export const useRenameDocumentMutation = () => {
  const documentApi = useDocumentApi();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      id,
      originalName,
      organizationId,
    }: {
      id: string;
      originalName: string;
      organizationId: string;
    }) => documentApi.renameDocument(id, originalName),
    onSuccess: (response, variables) => {
      // Invalidate documents list
      queryClient.invalidateQueries({
        queryKey: queryKeys.documents(variables.organizationId),
      });
    },
  });
};

// Organization Hooks
export const useOrganizationsQuery = () => {
  const organizationApi = useOrganizationApi();

  return useQuery({
    queryKey: queryKeys.organizations(),
    queryFn: () => organizationApi.getOrganizations(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Conversation Hooks
export const useConversationsQuery = (organizationId: string) => {
  const conversationApi = useConversationApi();

  return useQuery({
    queryKey: queryKeys.conversations(organizationId),
    queryFn: () => conversationApi.getConversations(organizationId),
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useConversationQuery = (id: string) => {
  const conversationApi = useConversationApi();

  return useQuery({
    queryKey: queryKeys.conversation(id),
    queryFn: () => conversationApi.getConversation(id),
    enabled: !!id,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useCreateConversationMutation = () => {
  const conversationApi = useConversationApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { title: string; organizationId: string }) =>
      conversationApi.createConversation(data.title, data.organizationId),
    onSuccess: (response, variables) => {
      // Invalidate conversations list
      queryClient.invalidateQueries({
        queryKey: queryKeys.conversations(variables.organizationId),
      });
    },
  });
};

export const useDeleteConversationMutation = () => {
  const conversationApi = useConversationApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => conversationApi.deleteConversation(id),
    onSuccess: (response, id) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({ queryKey: queryKeys.conversation(id) });
      queryClient.invalidateQueries({ queryKey: ["conversations"] });
    },
  });
};

// OneDrive Hooks
export const useOneDriveStatusQuery = () => {
  const oneDriveApi = useOneDriveApi();

  return useQuery({
    queryKey: queryKeys.onedrive.status(),
    queryFn: () => oneDriveApi.isOneDriveConnected(),
    staleTime: 30 * 1000, // 30 seconds
    retry: false,
  });
};

export const useOneDriveItemsQuery = (
  path: string,
  enabled: boolean = true
) => {
  const oneDriveApi = useOneDriveApi();

  return useQuery({
    queryKey: queryKeys.onedrive.items(path),
    queryFn: () => oneDriveApi.listOneDriveFiles(path),
    enabled: enabled && !!path,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useOneDriveAddToRAGMutation = () => {
  const oneDriveApi = useOneDriveApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: OneDriveAddToRAGRequest) =>
      oneDriveApi.addOneDriveFileToRAG(request),
    onSuccess: (response, variables) => {
      // Invalidate documents list to show the newly added file
      queryClient.invalidateQueries({
        queryKey: queryKeys.documents(variables.organizationId),
      });
    },
  });
};
