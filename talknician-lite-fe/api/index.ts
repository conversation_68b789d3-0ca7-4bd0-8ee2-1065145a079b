// Main API exports - centralized access to all API clients
export {
  BaseApiClient,
  createBase<PERSON>pi<PERSON>lient,
  useBaseApiClient,
  createApiClientWithStoredToken,
} from "./base";

// Persona API
export {
  PersonaApiClient,
  createPersonaApiClient,
  usePersonaApi,
} from "./personas";

// Conversation API
export {
  ConversationApiClient,
  createConversationApiClient,
  useConversationApi,
} from "./conversations";

// Chat API
export { ChatApiClient, createChatApiClient, useChatApi } from "./chat";

// Document API
export {
  DocumentApiClient,
  createDocumentApiClient,
  useDocument<PERSON>pi,
  type Document,
  type DocumentsResponse,
  type DocumentResponse,
} from "./documents";

// Organization API
export {
  OrganizationApiClient,
  createOrganizationApiClient,
  useOrganizationApi,
  type Organization,
  type OrganizationsResponse,
  type OrganizationResponse,
} from "./organizations";

// OneDrive API
export {
  OneDriveApiClient,
  createOneDriveApiClient,
  useOneDrive<PERSON><PERSON>,
  type OneDriveItem,
  type OneDriveAddToRAGRequest,
} from "./onedrive";

// React Query Hooks
export {
  useDocumentsQuery,
  useUploadDocumentMutation,
  useDeleteDocumentMutation,
  useAddDocumentToRAGMutation,
  useRemoveDocumentFromRAGMutation,
  useRenameDocumentMutation,
  useOrganizationsQuery,
  useConversationsQuery,
  useConversationQuery,
  useCreateConversationMutation,
  useDeleteConversationMutation,
  useOneDriveStatusQuery,
  useOneDriveItemsQuery,
  useOneDriveAddToRAGMutation,
  queryKeys,
} from "./hooks";

// Convenience hooks for common combinations
import { useAuth } from "@/contexts/AuthContext";

// Combined API hook that provides access to all API clients
export const useApi = () => {
  const { accessToken } = useAuth();

  return {
    personas: new (require("./personas").PersonaApiClient)(
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
      () => ({
        "Content-Type": "application/json",
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
      })
    ),
    conversations: new (require("./conversations").ConversationApiClient)(
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
      () => ({
        "Content-Type": "application/json",
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
      })
    ),
    chat: new (require("./chat").ChatApiClient)(
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
      () => ({
        "Content-Type": "application/json",
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
      })
    ),
    documents: new (require("./documents").DocumentApiClient)(
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
      () => ({
        "Content-Type": "application/json",
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
      })
    ),
    organizations: new (require("./organizations").OrganizationApiClient)(
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
      () => ({
        "Content-Type": "application/json",
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
      })
    ),
    onedrive: new (require("./onedrive").OneDriveApiClient)(
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
      () => ({
        "Content-Type": "application/json",
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
      })
    ),
  };
};
