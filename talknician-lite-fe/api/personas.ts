import { BaseApiClient, ApiResponse } from "./base";
import { ChatPersona } from "@/types/chat";
import { useAuth } from "@/contexts/AuthContext";

// Persona API response types
interface PersonasResponse {
  personas: ChatPersona[];
}

interface PersonaResponse {
  persona: ChatPersona;
}

// Persona API Client
export class PersonaApiClient extends BaseApiClient {
  async getAllPersonas(): Promise<ChatPersona[]> {
    const response = await this.request<ApiResponse<PersonasResponse>>(
      "/api/personas"
    );
    return response.data.personas;
  }

  async getPersonaById(personaId: string): Promise<ChatPersona | null> {
    try {
      const response = await this.request<ApiResponse<PersonaResponse>>(
        `/api/personas/${personaId}`
      );
      return response.data.persona;
    } catch (error) {
      console.error("Error loading persona:", error);
      return null;
    }
  }

  async createPersona(personaData: {
    title: string;
    systemPrompt: string;
    isPublic?: boolean;
  }): Promise<ChatPersona> {
    const response = await this.request<ApiResponse<PersonaResponse>>(
      "/api/personas",
      {
        method: "POST",
        body: JSON.stringify(personaData),
      }
    );
    return response.data.persona;
  }

  async updatePersona(
    personaId: string,
    personaData: {
      title?: string;
      systemPrompt?: string;
      isPublic?: boolean;
    }
  ): Promise<ChatPersona> {
    const response = await this.request<ApiResponse<PersonaResponse>>(
      `/api/personas/${personaId}`,
      {
        method: "PUT",
        body: JSON.stringify(personaData),
      }
    );
    return response.data.persona;
  }

  async deletePersona(personaId: string): Promise<void> {
    await this.request(`/api/personas/${personaId}`, {
      method: "DELETE",
    });
  }
}

// Factory functions
export const createPersonaApiClient = (
  accessToken: string | null
): PersonaApiClient => {
  return new PersonaApiClient(
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
    () => ({
      "Content-Type": "application/json",
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    })
  );
};

// React hook
export const usePersonaApi = (): PersonaApiClient => {
  const { accessToken } = useAuth();

  return createPersonaApiClient(accessToken);
};
