"use client";

import type React from "react";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import {
  FolderOpen,
  Users,
  Settings,
  ChevronDown,
  ChevronRight,
  User,
  Menu,
  Rocket,
} from "lucide-react";
import { ThemeToggle } from "@/components/theme-toggle";
import { UserProfile } from "@/components/user-profile";
import { OrganizationSelector } from "@/components/organization-selector";
import { useAuth } from "@/contexts/AuthContext";

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  children?: NavigationItem[];
}

const navigation: NavigationItem[] = [
  {
    name: "Houston",
    href: "/houston",
    icon: Rocket,
    badge: "AI Chat",
  },
  {
    name: "Knowledge Base",
    href: "/integrations",
    icon: FolderOpen,
  },
  {
    name: "Organization",
    href: "/organization",
    icon: Users,
  },
];

const settingsNavigation: NavigationItem[] = [
  {
    name: "Settings",
    href: "/settings",
    icon: Settings,
    children: [
      {
        name: "Profile",
        href: "/settings/profile",
        icon: User,
      },
      // {
      //   name: "Plans & Billing",
      //   href: "/settings/billing",
      //   icon: CreditCard,
      // },
    ],
  },
  // {
  //   name: "Help & Support",
  //   href: "/help",
  //   icon: HelpCircle,
  // },
];

function SidebarContent({ onItemClick }: { onItemClick?: () => void }) {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>(["Settings"]);
  const { user, isAuthenticated } = useAuth();

  const toggleExpanded = (itemName: string) => {
    setExpandedItems((prev) =>
      prev.includes(itemName)
        ? prev.filter((item) => item !== itemName)
        : [...prev, itemName]
    );
  };

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(href + "/");
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.name);
    const active = isActive(item.href);

    return (
      <div key={item.name}>
        <div className={`flex items-center ${level > 0 ? "ml-6" : ""}`}>
          {hasChildren ? (
            <button
              onClick={() => toggleExpanded(item.name)}
              className={`flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                active
                  ? "bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300"
                  : "text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800"
              }`}
            >
              <item.icon className="w-5 h-5 mr-3 flex-shrink-0" />
              <span className="flex-1 text-left">{item.name}</span>
              {isExpanded ? (
                <ChevronDown className="w-4 h-4 ml-2" />
              ) : (
                <ChevronRight className="w-4 h-4 ml-2" />
              )}
            </button>
          ) : (
            <Link
              href={item.href}
              onClick={onItemClick}
              className={`flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                active
                  ? "bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300"
                  : "text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800"
              }`}
            >
              <item.icon className="w-5 h-5 mr-3 flex-shrink-0" />
              <span className="flex-1">{item.name}</span>
              {item.badge && (
                <Badge variant="secondary" className="ml-2 text-xs">
                  {item.badge}
                </Badge>
              )}
            </Link>
          )}
        </div>
        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children?.map((child) =>
              renderNavigationItem(child, level + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <div className="space-y-2">
          {navigation.map((item) => renderNavigationItem(item))}
        </div>

        <Separator className="my-6" />

        <div className="space-y-2">
          {settingsNavigation.map((item) => renderNavigationItem(item))}
        </div>
      </ScrollArea>

      {/* User Profile */}
      <div className="p-4 border-t border-slate-200 dark:border-slate-700">
        {isAuthenticated && user ? (
          <UserProfile
            showName={true}
            className="w-full justify-start p-3 bg-slate-50 dark:bg-slate-800 rounded-lg"
          />
        ) : (
          <div className="flex items-center justify-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
            <span className="text-sm text-slate-500 dark:text-slate-400">
              Not signed in
            </span>
          </div>
        )}
      </div>
    </div>
  );
}

export function SidebarNavigation() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  // Close mobile sidebar when screen size changes
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsMobileOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <>
      {/* Mobile Navigation */}
      <div className="md:hidden">
        <Sheet open={isMobileOpen} onOpenChange={setIsMobileOpen}>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="fixed top-4 left-4 z-50 h-10 w-10 p-0 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 shadow-lg"
            >
              <Menu className="h-5 w-5" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-64 p-0">
            <div className="flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700">
              <Link href="/" className="flex items-center space-x-2">
                <img src="/long-logo.png" alt="Talknician Logo" />
                {/* <span className="text-md font-bold text-slate-900 dark:text-white">
                  TALKNICIAN
                </span> */}
              </Link>
              <ThemeToggle />
            </div>
            <SidebarContent onItemClick={() => setIsMobileOpen(false)} />
          </SheetContent>
        </Sheet>
      </div>

      {/* Desktop Navigation */}
      <div
        className={`hidden md:flex flex-col h-full bg-white dark:bg-slate-900 border-r border-slate-200 dark:border-slate-700 transition-all duration-300 ${
          isCollapsed ? "w-16" : "w-64"
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700">
          {!isCollapsed && (
            <Link href="/" className="flex items-center space-x-2">
              <img src="/long-logo.png" alt="Talknician Logo" />
              {/* <span className="text-md font-bold text-slate-900 dark:text-white">
                TALKNICIAN
              </span> */}
            </Link>
          )}
          <div className="flex items-center space-x-2">
            {!isCollapsed && <ThemeToggle />}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0"
            >
              <Menu className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {isCollapsed ? (
          <div className="flex flex-col items-center py-4 space-y-4">
            {navigation.map((item) => (
              <div key={item.name} className="relative group">
                <Link
                  href={item.href}
                  className="flex items-center justify-center w-10 h-10 text-sm font-medium rounded-lg transition-colors text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800"
                  title={item.name}
                >
                  <item.icon className="w-5 h-5" />
                </Link>
                <div className="absolute left-full ml-2 px-2 py-1 bg-slate-900 dark:bg-slate-700 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                  {item.name}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <SidebarContent />
        )}
      </div>
    </>
  );
}
