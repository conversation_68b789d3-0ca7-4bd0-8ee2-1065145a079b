import express from "express";
import { authenticate } from "../middleware/auth.middleware";
import { pusher } from "../services/pusher.service";
import logger from "@/utils/logger";

const router: express.Router = express.Router();

router.post("/auth", authenticate, (req, res) => {
  if (!pusher) {
    return res.status(500).send("Pusher is not configured");
  }

  const socketId = req.body.socket_id;
  const channel = req.body.channel_name;
  const { organizationId } = req.body;
  const userId = req.user?.id;

  if (!userId || !organizationId) {
    return res.status(403).send("Forbidden");
  }

  // Very basic authorization: check if user belongs to the organization from the channel name
  // Channel name format: private-org-ORGANIZATION_ID
  const expectedChannel = `private-org-${organizationId}`;
  if (channel !== expectedChannel) {
    logger.warn("Pusher auth failed: channel mismatch", {
      userId,
      organizationId,
      requestedChannel: channel,
      expectedChannel,
    });
    return res.status(403).send("Forbidden");
  }

  // In a real app, you'd check if `userId` is actually a member of `organizationId`
  // For now, we assume if they provide the correct orgId, they are a member.

  const presenceData = {
    user_id: userId,
  };

  try {
    const auth = pusher.authorizeChannel(socketId, channel, presenceData);
    res.send(auth);
  } catch (error) {
    logger.error("Pusher authentication failed", { error });
    res.status(500).send("Pusher authentication error");
  }
});

export default router;
